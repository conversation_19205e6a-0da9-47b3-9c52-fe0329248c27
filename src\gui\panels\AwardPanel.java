package gui.panels;

import service.MusicService;
import gui.MusicStreamingGUI;

import javax.swing.*;
import java.awt.*;

/**
 * Panel for managing awards in the music streaming application
 * TODO: Implement full functionality similar to ArtistPanel
 */
public class AwardPanel extends JPanel {

    private MusicService musicService;
    private MusicStreamingGUI parentFrame;

    public AwardPanel(MusicService musicService, MusicStreamingGUI parentFrame) {
        this.musicService = musicService;
        this.parentFrame = parentFrame;
        initializePanel();
    }

    private void initializePanel() {
        setLayout(new BorderLayout());

        // Placeholder content
        JLabel label = new JLabel("Award Management - Coming Soon", SwingConstants.CENTER);
        label.setFont(label.getFont().deriveFont(18f));
        add(label, BorderLayout.CENTER);

        JLabel infoLabel = new JLabel("This panel will contain award management functionality", SwingConstants.CENTER);
        add(infoLabel, BorderLayout.SOUTH);
    }

    public void refreshData() {
        // TODO: Implement data refresh
    }
}
