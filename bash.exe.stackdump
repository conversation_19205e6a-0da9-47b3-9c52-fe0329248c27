Stack trace:
Frame         Function      Args
0007FFFFB730  00021005FEBA (000210285F48, 00021026AB6E, 000000000000, 0007FFFFA630) msys-2.0.dll+0x1FEBA
0007FFFFB730  0002100467F9 (000000000000, 000000000000, 000000000000, 0007FFFFBA08) msys-2.0.dll+0x67F9
0007FFFFB730  000210046832 (000210285FF9, 0007FFFFB5E8, 000000000000, 000000000000) msys-2.0.dll+0x6832
0007FFFFB730  000210068F86 (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28F86
0007FFFFB730  0002100690B4 (0007FFFFB740, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x290B4
0007FFFFBA10  00021006A49D (0007FFFFB740, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A49D
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFA79FB0000 ntdll.dll
7FFA78BA0000 KERNEL32.DLL
7FFA77750000 KERNELBASE.dll
7FFA78680000 USER32.dll
7FFA77B60000 win32u.dll
7FFA78650000 GDI32.dll
7FFA77510000 gdi32full.dll
7FFA77300000 msvcp_win.dll
7FFA77630000 ucrtbase.dll
000210040000 msys-2.0.dll
7FFA798D0000 advapi32.dll
7FFA788E0000 msvcrt.dll
7FFA790F0000 sechost.dll
7FFA77B30000 bcrypt.dll
7FFA797B0000 RPCRT4.dll
7FFA76770000 CRYPTBASE.DLL
7FFA770C0000 bcryptPrimitives.dll
7FFA79A00000 IMM32.DLL
